import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import FileUpload from './FileUpload/FileUpload';

interface RHFFileUploadProps {
  name: string;
  label?: string;
  maxSizeInMB?: number;
  acceptedFileTypes?: string;
  showDeleteOption?: boolean;
  onDeleteStateChange?: (isDeleted: boolean) => void;
}

export const RHFFileUpload: React.FC<RHFFileUploadProps> = ({
  name,
  label,
  maxSizeInMB = 2,
  acceptedFileTypes = 'image/*',
  showDeleteOption = false,
  onDeleteStateChange,
}) => {
  const { control, formState: { errors } } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <FileUpload
          label={label}
          initialValue={field.value}
          onChange={(value) => field.onChange(value)}
          onDeleteStateChange={onDeleteStateChange}
          maxSizeInMB={maxSizeInMB}
          acceptedFileTypes={acceptedFileTypes}
          error={errorMessage}
          showDeleteOption={showDeleteOption}
        />
      )}
    />
  );
};
