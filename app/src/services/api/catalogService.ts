/**
 * Service for handling catalog-related API operations
 * Integrates with the backend API endpoints for categories, subcategories, and products
 */

import { apiClient } from './client';
import {
  Category,
  Subcategory,
  Product,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateSubcategoryRequest,
  UpdateSubcategoryRequest,
  CreateProductRequest,
  UpdateProductRequest,
} from '@/types/catalog.types';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

// Organization ID for testing/development
const ORGANIZATION_ID = 40928446087168;

/**
 * Service class for catalog management operations
 */
export class CatalogService {
  // Base URLs for different catalog entities - matching backend endpoints
  private static CATEGORIES_URL = '/v1/product-categories';
  private static PRODUCTS_URL = '/v1/products';

  /**
   * Get all categories for the organization
   * @param params Query parameters (not used for pagination as backend returns all)
   * @returns Promise with categories data
   */
  static async getCategories(params?: QueryParams): Promise<PaginatedResponse<Category>> {
    try {
      const response = await apiClient.get<Category[]>(
        `${this.CATEGORIES_URL}?organizationId=${ORGANIZATION_ID}`
      );

      // Since backend doesn't support pagination for categories, we'll simulate it
      let categories = response || [];

      // Apply sorting if provided
      if (params?.sort) {
        const [field, order] = params.sort.split(',');
        categories.sort((a: any, b: any) => {
          const aValue = a[field] || '';
          const bValue = b[field] || '';
          if (order === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
      } else {
        // Default sort by name
        categories.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
      }

      // Calculate pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      // Get paginated data
      const paginatedData = categories.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        total: categories.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(categories.length / limit)
      };
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  /**
   * Get a category by ID
   * @param id Category ID
   * @returns Promise with category data
   */
  static async getCategoryById(id: number): Promise<ApiResponse<Category>> {
    try {
      const response = await apiClient.get<Category>(`${this.CATEGORIES_URL}/${id}`);
      return {
        data: response,
        message: 'Category retrieved successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error fetching category with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new category
   * @param data Category data
   * @returns Promise with created category data
   */
  static async createCategory(data: CreateCategoryRequest): Promise<ApiResponse<Category>> {
    try {
      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('organizationId', (data.organizationId || ORGANIZATION_ID).toString());

      if (data.imageFile) {
        formData.append('imageFile', data.imageFile);
      }

      const response = await apiClient.post<Category>(this.CATEGORIES_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return {
        data: response,
        message: 'Category created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  /**
   * Update an existing category
   * @param id Category ID
   * @param data Updated category data
   * @returns Promise with updated category data
   */
  static async updateCategory(id: number, data: UpdateCategoryRequest): Promise<ApiResponse<Category>> {
    try {
      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('organizationId', (data.organizationId || ORGANIZATION_ID).toString());

      // Add deleteImage flag - defaults to false if not provided
      formData.append('deleteImage', (data.deleteImage || false).toString());

      if (data.imageFile) {
        formData.append('imageFile', data.imageFile);
      }

      const response = await apiClient.put<Category>(`${this.CATEGORIES_URL}/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return {
        data: response,
        message: 'Category updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating category with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a category
   * @param id Category ID
   * @returns Promise with deletion response
   */
  static async deleteCategory(id: number): Promise<ApiResponse<void>> {
    try {
      await apiClient.delete<void>(`${this.CATEGORIES_URL}/${id}`);
      return {
        data: undefined,
        message: 'Category deleted successfully',
        status: 204
      };
    } catch (error) {
      console.error(`Error deleting category with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all subcategories for the organization
   * @param params Query parameters for filtering and sorting
   * @returns Promise with paginated subcategory data
   */
  static async getSubcategories(params?: QueryParams): Promise<PaginatedResponse<Subcategory>> {
    try {
      // If category_id is provided in filter, get subcategories for that category
      if (params?.filter?.category_id) {
        const categoryId = params.filter.category_id;

        // Ensure categoryId is a valid number
        const numericCategoryId = typeof categoryId === 'string' ? parseInt(categoryId) : categoryId;

        if (typeof numericCategoryId === 'number' && !isNaN(numericCategoryId)) {
          return this.getSubcategoriesByCategoryId(numericCategoryId, params);
        } else {
          console.error('Invalid category_id in filter:', categoryId);
          // Fall through to get all subcategories
        }
      }

      // Use the new endpoint to get all subcategories for the organization
      const subcategoriesResponse = await apiClient.get<Subcategory[]>(
        `${this.CATEGORIES_URL}/sub-categories?organizationId=${ORGANIZATION_ID}`
      );

      let allSubcategories = subcategoriesResponse || [];

      // Get category names for UI display
      const categoriesResponse = await apiClient.get<Category[]>(
        `${this.CATEGORIES_URL}?organizationId=${ORGANIZATION_ID}`
      );
      const categories = categoriesResponse || [];

      // Add category names to subcategories
      allSubcategories = allSubcategories.map(sub => {
        const category = categories.find(cat => cat.id === sub.categoryId);
        return {
          ...sub,
          categoryName: category?.name || 'Unknown'
        };
      });

      // Apply sorting
      if (params?.sort) {
        const [field, order] = params.sort.split(',');
        allSubcategories.sort((a: any, b: any) => {
          const aValue = a[field] || '';
          const bValue = b[field] || '';
          if (order === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
      } else {
        // Default sort by name
        allSubcategories.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
      }

      // Calculate pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedData = allSubcategories.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        total: allSubcategories.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(allSubcategories.length / limit)
      };
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      throw error;
    }
  }

  /**
   * Get subcategories by category ID
   * @param categoryId Category ID
   * @param params Query parameters for pagination, sorting, and filtering
   * @returns Promise with paginated subcategory data
   */
  static async getSubcategoriesByCategoryId(categoryId: number, params?: QueryParams): Promise<PaginatedResponse<Subcategory>> {
    try {
      // Validate categoryId
      if (typeof categoryId !== 'number' || isNaN(categoryId)) {
        throw new Error(`Invalid categoryId: ${categoryId}. Expected a number.`);
      }

      // Updated: organizationId is no longer required for this endpoint
      const response = await apiClient.get<Subcategory[]>(
        `${this.CATEGORIES_URL}/${categoryId}/sub-categories`
      );

      let subcategories = response || [];

      // Get category name for UI display
      try {
        const categoryResponse = await apiClient.get<Category>(`${this.CATEGORIES_URL}/${categoryId}`);
        subcategories = subcategories.map(sub => ({
          ...sub,
          categoryName: categoryResponse.name
        }));
      } catch (error) {
        console.warn(`Error fetching category name for category ${categoryId}:`, error);
      }

      // Apply sorting
      if (params?.sort) {
        const [field, order] = params.sort.split(',');
        subcategories.sort((a: any, b: any) => {
          const aValue = a[field] || '';
          const bValue = b[field] || '';
          if (order === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
      } else {
        // Default sort by name
        subcategories.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
      }

      // Calculate pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedData = subcategories.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        total: subcategories.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(subcategories.length / limit)
      };
    } catch (error) {
      console.error(`Error fetching subcategories for category ID ${categoryId}:`, error);
      throw error;
    }
  }

  /**
   * Get a subcategory by ID
   * @param categoryId Category ID
   * @param id Subcategory ID
   * @returns Promise with subcategory data
   */
  static async getSubcategoryById(categoryId: number, id: number): Promise<ApiResponse<Subcategory>> {
    try {
      const response = await apiClient.get<Subcategory>(`${this.CATEGORIES_URL}/${categoryId}/sub-categories/${id}`);
      return {
        data: response,
        message: 'Subcategory retrieved successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error fetching subcategory with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new subcategory
   * @param data Subcategory data
   * @returns Promise with created subcategory data
   */
  static async createSubcategory(data: CreateSubcategoryRequest): Promise<ApiResponse<Subcategory>> {
    try {
      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('categoryId', data.categoryId.toString());
      formData.append('organizationId', (data.organizationId || ORGANIZATION_ID).toString());

      if (data.imageFile) {
        formData.append('imageFile', data.imageFile);
      }

      const response = await apiClient.post<Subcategory>(
        `${this.CATEGORIES_URL}/${data.categoryId}/sub-categories`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return {
        data: response,
        message: 'Subcategory created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating subcategory:', error);
      throw error;
    }
  }

  /**
   * Update an existing subcategory
   * @param categoryId Category ID
   * @param id Subcategory ID
   * @param data Updated subcategory data
   * @returns Promise with updated subcategory data
   */
  static async updateSubcategory(categoryId: number, id: number, data: UpdateSubcategoryRequest): Promise<ApiResponse<Subcategory>> {
    try {
      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('categoryId', data.categoryId.toString());
      formData.append('organizationId', (data.organizationId || ORGANIZATION_ID).toString());

      if (data.imageFile) {
        formData.append('imageFile', data.imageFile);
      }

      const response = await apiClient.put<Subcategory>(
        `${this.CATEGORIES_URL}/${categoryId}/sub-categories/${id}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return {
        data: response,
        message: 'Subcategory updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating subcategory with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a subcategory
   * @param categoryId Category ID
   * @param id Subcategory ID
   * @returns Promise with deletion response
   */
  static async deleteSubcategory(categoryId: number, id: number): Promise<ApiResponse<void>> {
    try {
      await apiClient.delete<void>(`${this.CATEGORIES_URL}/${categoryId}/sub-categories/${id}`);
      return {
        data: undefined,
        message: 'Subcategory deleted successfully',
        status: 204
      };
    } catch (error) {
      console.error(`Error deleting subcategory with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all products for the organization
   * @param params Query parameters for filtering and sorting
   * @returns Promise with paginated product data
   */
  static async getProducts(params?: QueryParams): Promise<PaginatedResponse<Product>> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append('organizationId', ORGANIZATION_ID.toString());

      const response = await apiClient.get<Product[]>(`${this.PRODUCTS_URL}?${queryParams.toString()}`);
      let products = response || [];

      // Enhance products with category and subcategory names for UI display
      const enhancedProducts = await Promise.all(
        products.map(async (product) => {
          let categoryName = '';
          let subcategoryName = '';

          try {
            // Get category info
            if (product.categoryId) {
              const categoryResponse = await apiClient.get<Category>(`${this.CATEGORIES_URL}/${product.categoryId}`);
              categoryName = categoryResponse.name;
            }

            // Get subcategory info
            if (product.subCategoryId && product.categoryId) {
              const subcategoryResponse = await apiClient.get<Subcategory>(
                `${this.CATEGORIES_URL}/${product.categoryId}/sub-categories/${product.subCategoryId}`
              );
              subcategoryName = subcategoryResponse.name;
            }
          } catch (error) {
            console.warn(`Error fetching category/subcategory info for product ${product.id}:`, error);
          }

          return {
            ...product,
            categoryName,
            subcategoryName
          };
        })
      );

      // Apply filtering if provided
      let filteredProducts = enhancedProducts;
      if (params?.filter) {
        const filters = params.filter;

        // Filter by subcategory_id if provided (null means "All Subcategories", so skip filtering)
        if (filters.subcategory_id !== null && filters.subcategory_id !== undefined) {
          filteredProducts = filteredProducts.filter(
            product => product.subCategoryId === filters.subcategory_id
          );
        }

        // Filter by category_id if provided (null means "All Categories", so skip filtering)
        if (filters.category_id !== null && filters.category_id !== undefined) {
          filteredProducts = filteredProducts.filter(
            product => product.categoryId === filters.category_id
          );
        }

        // Filter by name if provided (case-insensitive partial match)
        if (filters.name) {
          const searchTerm = filters.name.toString().toLowerCase();
          filteredProducts = filteredProducts.filter(
            product => product.name.toLowerCase().includes(searchTerm)
          );
        }
      }

      // Apply sorting
      if (params?.sort) {
        const [field, order] = params.sort.split(',');
        filteredProducts.sort((a: any, b: any) => {
          const aValue = a[field] || '';
          const bValue = b[field] || '';
          if (order === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
      } else {
        // Default sort by name
        filteredProducts.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
      }

      // Calculate pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedData = filteredProducts.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        total: filteredProducts.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(filteredProducts.length / limit)
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  /**
   * Get a product by ID
   * @param id Product ID
   * @returns Promise with product data
   */
  static async getProductById(id: number): Promise<ApiResponse<Product>> {
    try {
      const response = await apiClient.get<Product>(`${this.PRODUCTS_URL}/${id}`);
      return {
        data: response,
        message: 'Product retrieved successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error fetching product with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new product
   * @param data Product data
   * @returns Promise with created product data
   */
  static async createProduct(data: CreateProductRequest): Promise<ApiResponse<Product>> {
    try {
      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('price', data.price.toString());
      formData.append('categoryId', data.categoryId.toString());
      formData.append('subCategoryId', data.subCategoryId.toString());
      formData.append('organizationId', (data.organizationId || ORGANIZATION_ID).toString());

      // Append image files if provided
      if (data.imageFiles && data.imageFiles.length > 0) {
        data.imageFiles.forEach((file) => {
          formData.append('imageFiles', file);
        });
      }

      const response = await apiClient.post<Product>(this.PRODUCTS_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return {
        data: response,
        message: 'Product created successfully',
        status: 201
      };
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  /**
   * Update an existing product
   * @param id Product ID
   * @param data Updated product data
   * @returns Promise with updated product data
   */
  static async updateProduct(id: number, data: UpdateProductRequest): Promise<ApiResponse<Product>> {
    try {
      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('price', data.price.toString());
      formData.append('categoryId', data.categoryId.toString());
      formData.append('subCategoryId', data.subCategoryId.toString());
      formData.append('organizationId', (data.organizationId || ORGANIZATION_ID).toString());

      // Append image files if provided
      if (data.imageFiles && data.imageFiles.length > 0) {
        data.imageFiles.forEach((file) => {
          formData.append('imageFiles', file);
        });
      }

      const response = await apiClient.put<Product>(`${this.PRODUCTS_URL}/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return {
        data: response,
        message: 'Product updated successfully',
        status: 200
      };
    } catch (error) {
      console.error(`Error updating product with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a product
   * @param id Product ID
   * @returns Promise with deletion response
   */
  static async deleteProduct(id: number): Promise<ApiResponse<void>> {
    try {
      await apiClient.delete<void>(`${this.PRODUCTS_URL}/${id}`);
      return {
        data: undefined,
        message: 'Product deleted successfully',
        status: 204
      };
    } catch (error) {
      console.error(`Error deleting product with ID ${id}:`, error);
      throw error;
    }
  }
}
